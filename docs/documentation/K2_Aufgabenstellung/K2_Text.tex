\chapter{Aufgabenstellung}

\subsection*{Ausgangspunkt des Studienprojekts: Objekterkennung zur Inventarüberwachung eines Kühlschranks}

Das Studienprojekt befasst sich mit der Entwicklung eines Systems zur Objekterkennung, das in der Lage ist, den Inhalt eines Kühlschranks zu überwachen.
Ziel ist es, eine Lösung zu finden, die den Benutzern hilft, ihre Lebensmittelbestände digital zu erfassen.

Dies geschieht durch die Erkennung von Objekten auf einem Kamerabild,
das von einer Kamera aufgenommen wird, die oben am Kühlschrank installiert ist
und nach unten auf den Boden vor dem Kühlschrank gerichtet ist.

Zu Beginn des Projekts wurden folgende Kriterien für die Lösung definiert:
\begin{itemize}
    \item Musskriterien: Die Lösung muss in der Lage sein, die Objekte zu klassifizieren und nachzuverfolgen.
    Anhand der Bewegung wird erkan<PERSON>, ob ein Objekt in den Kühlschrank gelegt oder herausgenommen wurde.
    Die Lösung kann auch mit Objekten umgehen, die teilweise von einer Hand verdeckt sind.

    \item Kannkriterien: Die Lösung kann mit mehreren Objekten umgehen, die sich gleichzeitig im Sichtfeld der Kamera befinden.
    Die Lösung kann mit unbekannten Objekten umgehen, die nicht exakt so im Trainingsdatensatz enthalten sind.

    \item Ausschlusskriterien: Die Lösung muss nicht in der Lage sein, Objekte zu erkennen, die sich außerhalb des Sichtfelds der Kamera befinden.
    Die Lösung beschränkt sich auf die Erkennung weniger ausgewählter Produkttypen,
    die exemplarisch gewählt wurden, um die Komplexität des Projekts zu reduzieren.
\end{itemize}

Insgesamt strebt das Studienprojekt danach, ein System zu entwickeln,
das grundsätzlich in der Lage ist, Objekte zu erkennen und deren Bewegung zu verfolgen
und das Konzept der Inventarüberwachung eines Kühlschranks mittels Kamera in Vogelperspektive
zu demonstrieren und zu prüfen.
