\chapter{Verwendete Tools}

\begin{itemize}
    \item Entwicklungsumgebung: Visual Studio Code
    \item Build-Tools/-Umgebungen: anaconda
    \item Frameworks: opencv, mediapipe, scikit-learn, tensorflow, matplotlib, scipy, PySide6, pytorch
    \item Versionsverwaltung: Git, GitHub
    \item Dokumentation: Latex, bibtex
    \item Aufgabenteilung: GitHub Projects
\end{itemize}

Verwendete Conda Umgebungen sind Teil des Repositorys und sind dort als .yml Dateien abgelegt. In diesen sind die Versionen der Frameworks festgelegt.