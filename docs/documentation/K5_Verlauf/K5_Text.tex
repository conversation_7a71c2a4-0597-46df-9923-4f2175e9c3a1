\chapter{<PERSON><PERSON><PERSON><PERSON>}
\section{Ist-/Sollvergleich: Projektplan}

\subsection{Soll-Verlauf}
Geplant war für den Projektablauf ein geradliniger Ablauf, bei dem jeder Abschnitt auf die Ergebnisse des Vorherigen aufbaut.
Es wurde eine Pause für die Klausurenphase einkalkuliert, wofür die verbleibenden Themen in die vorlesungsfreie Zeit geschoben
wurden.

\definecolor{barblue}{RGB}{153,204,254}
\definecolor{groupblue}{RGB}{51,102,254}
\definecolor{linkred}{RGB}{165,0,33}
\setganttlinklabel{f-s}{}
\newcounter{myWeekNum}
\stepcounter{myWeekNum}
\newcommand{\myWeek}{\themyWeekNum
    \stepcounter{myWeekNum}
    \ifnum\themyWeekNum=53
        \setcounter{myWeekNum}{1}
    \else\fi
}
\setcounter{myWeekNum}{9}
%German abbreviations; Use S. instead of September  
\def\pgfcalendarmonthname#1{%  
 \ifcase#1 Dezember \or <PERSON><PERSON>r\or <PERSON><PERSON><PERSON>\or <PERSON><PERSON>rz\or April\or Mai\or Juni\or Juli \or August \or September \or Oktober \or November \or Dezember\fi%  
}  
\ganttset{calendar week text={\myWeek{}}}
\begin{figure}[H]
    \hspace*{-50px}
    \begin{ganttchart}[
            canvas/.append style={fill=none, draw=black!5, line width=.75pt},
            hgrid style/.style={draw=black!5, line width=.75pt},
            vgrid={*{4}{draw=none},{draw=black!5, line width=.75pt},*{2}{draw=none}},
            x unit=0.6mm,
            y unit chart=0.6cm,
            y unit title=0.5cm,
            time slot format=isodate,
            title height=1,
            title/.style={draw=black!5, fill=none},
            title label font=\bfseries\footnotesize,
            bar label font=\mdseries\small\color{black!70},
            bar label node/.append style={left=0.5cm},
            bar/.append style={draw=none, fill=black!63},
            bar incomplete/.append style={fill=barblue},
            bar progress label font=\mdseries\footnotesize\color{black!70},
            group incomplete/.append style={fill=groupblue},
            group left shift=0,
            group right shift=0,
            group height=.5,
            group peaks tip position=0,
            group label node/.append style={left=0.5cm},
            group progress label font=\bfseries\small,
            link/.style={-latex, line width=1.5pt, linkred},
            link label font=\scriptsize\bfseries,
            link label node/.append style={below left=-2pt and 0pt},
        ]{2023-03-1}{2023-09-30}

        \gantttitlecalendar{year, month=name, week} \\
        \ganttbar[name=D1]{Sensoren Auswahl}{2023-03-29}{2023-4-6}\\
        \ganttbar[name=D]{Datenaufnahme}{2023-04-7}{2023-5-12} \\
        \ganttbar[]{Datenaufbereitung}{2023-5-13}{2023-6-1}\\
        \ganttbar[name=M]{Modell erstellen}{2023-06-2}{2023-6-30} \\
        \ganttbar[name=M1]{Testen und Evaluieren}{2023-07-28}{2023-08-10}\\
        \ganttbar[name=U]{User Interface}{2023-08-11}{2023-08-20} \\
        \ganttbar{Dokumentation}{2023-08-21}{2023-9-05}
    \end{ganttchart}
    \caption{Soll Gantt-Ablaufdiagram}
\end{figure}

\subsection{Ist-Verlauf}
Der tatsächliche Projektverlauf unterscheidet sich von dem geplanten Ablauf haupt\-sächlich dadurch, dass die einzelnen
Phasen nicht nacheinander starten, sondern bereits dann, sobald genug Ergebnisse vorlagen. Dies geschah zum
einen, um die Effizienz zu steigern, zum anderen, weil der anfängliche Plan einer sehr naiven Struktur folgte.

Die Phasen wurden im Vergleich zur initialen Planung weiter unterteilt, um eine präzisere Aufgabenteilung zu ermöglichen.

Die Datenaufnahme verzögerte sich aufgrund von Schwierigkeiten bei der Planung der Termine und der Notwendigkeit, das Aufnahmesetup mehrfach anpassen zu müssen,
um eine zufriedenstellende und für die Modelle ausreichende Qualität der Daten sicherstellen zu können.

Des Weiteren stellte sich erst im Verlauf der Modellentwicklung heraus, dass der bis dorthin entwickelte Modellansatz
nur ungenügende Ergebnisse lieferte, wodurch in weiteren Schritten ein anderes Modell entwickelt beziehungsweise getestet
werden musste.

Zu Beginn des Projekts war die Entwicklung eines User-Interfaces in Form einer mobilen App geplant, welche zwar begonnen wurde, jedoch aufgrund der auftretenden Probleme bei den Datenaufnahmen und der Notwendigkeit einer Demo-Applikation für das Postersymposium, nicht weiter verfolgt wurde.

\setcounter{myWeekNum}{9}
\ganttset{calendar week text={\myWeek{}}}
\begin{figure}[H]
    \hspace*{-80px}
    \begin{ganttchart}[
            canvas/.append style={fill=none, draw=black!5, line width=.75pt},
            hgrid style/.style={draw=black!5, line width=.75pt},
            vgrid={*{4}{draw=none},{draw=black!5, line width=.75pt},*{2}{draw=none}},
            x unit=0.6mm,
            y unit chart=0.6cm,
            y unit title=0.5cm,
            time slot format=isodate,
            title height=1,
            title/.style={draw=black!5, fill=none},
            title label font=\bfseries\footnotesize,
            bar label font=\mdseries\small\color{black!70},
            bar label node/.append style={left=0.5cm},
            bar/.append style={draw=none, fill=black!63},
            bar incomplete/.append style={fill=barblue},
            bar progress label font=\mdseries\footnotesize\color{black!70},
            group incomplete/.append style={fill=groupblue},
            group left shift=0,
            group right shift=0,
            group height=.5,
            group peaks tip position=0,
            group label node/.append style={left=0.5cm},
            group progress label font=\bfseries\small,
            link/.style={-latex, line width=1.5pt, linkred},
            link label font=\scriptsize\bfseries,
            link label node/.append style={below left=-2pt and 0pt},
        ]{2023-03-1}{2023-09-30}

        \gantttitlecalendar{year, month=name, week} \\
        \ganttgroup[name=D]{Datenaufnahme}{2023-03-29}{2023-5-25} \\
        \ganttbar[name=D1]{Sensoren Auswahl}{2023-03-29}{2023-4-6}\\
        \ganttbar[name=D2]{Test Aufnahme und Evaluierung}{2023-04-12}{2023-4-18}\\
        \ganttbar[]{Erste Aufnahme mit Gehörlosen}{2023-04-18}{2023-04-18}\\
        \ganttbar[]{Datenaufbereitung}{2023-4-19}{2023-4-29}\\
        \ganttbar[]{Anpassung Setup und Evaluation}{2023-5-4}{2023-5-10}\\
        \ganttbar[]{Multi-Cam Recording Skript}{2023-05-10}{2023-5-12}\\
        \ganttbar[]{Datenaufnahmen}{2023-05-9}{2023-5-25}\\
        [grid]\ganttgroup[name=M]{Modelle erstellen}{2023-05-15}{2023-6-30} \ganttgroup[name=M]{Modelle erstellen}{2023-07-31}{2023-8-16}\\
        \ganttbar[name=M1]{Testmodelle zur Datenüberprüfung}{2023-04-27}{2023-5-12}\\
        \ganttbar[name=M2]{LSTM Modell}{2023-5-15}{2023-6-22}\\
        \ganttbar[name=M3]{CorrNet}{2023-7-31}{2023-8-16}\\
        [grid]\ganttgroup[name=U]{Demo Applikation}{2023-06-1}{2023-6-30} \ganttgroup{}{2023-08-10}{2023-8-14}\\
        \ganttbar[name=U]{User Interface}{2023-05-05}{2023-05-20} \\
        \ganttgroup{Dokumentation}{2023-08-1}{2023-9-1}
    \end{ganttchart}
    \caption{Ist Gantt-Ablaufdiagram}
\end{figure}

\newpage
\section{Projektablauf}
\subsection{Eigenentwicklung eines Modells}
\paragraph*{Datenaufnahme} \mbox{}\\
Die Aufnahme der Gebärden sollte nicht nur in Hinblick auf das aktuelle Projekt erfolgen, sondern auch einen Datensatz erstellen, der zur Lösung anderer Aufgabenstellungen verwendet werden kann.
Folgende Sensoren standen zur Auswahl:
\begin{table}[H]
    \begin{center}
        \begin{tabular}[c]{ c P{4cm} c P{4cm} }
            Sensor      & Daten                     & Anschluss      & Schnittstelle            \\
            \hline
            NDI         & 6D Positionsdaten         & USB (trakSTAR) & Python-API (nur Windows) \\
            \hline
            iPhone/iPad & RGB-Bild, Tiefendaten     & -              & -                        \\
            \hline
            WebCam      & RGB-Bild                  & USB            & Python via OpenCV        \\
            \hline
            ZED 2i      & 2 RGB-Bilder, Tiefendaten & USB            & Python-API, Code-Beispiele
        \end{tabular}
    \end{center}
    \caption{Aufnahme Sensoren Übersicht}
\end{table}

Die erste Aufnahme zum Testen der Sensoren wurde mit einem Setup bestehend aus dem NDI-Trackingsystem und einer ZED 2i-Stereokamera
durchgeführt. Das NDI-System sollte dabei Positiondaten für jeden Sensor in Form von x, y und z Koordinaten
sowie den entsprechenden drei Winkeln liefern, die Stereokamera einfache RGB-Bilder und Tiefendaten.

Vor Beginn der Aufnahme musste ein Bezug zwischen dem Koordinatensystem des Trackingsystems und dem der Kamera hergestellt
werden. Dafür wurde ein Schachbrettmuster verwendet, bei dem die Sensoren an den Eckpunkten angebracht waren und dadurch
eine Verbindung zwischen dem sichtbaren Raum und den Positionsdaten herstellten. Für die eigentliche Aufnahme wurden an den
Fingerspitzen und dem Handrücken der gefilmten Person die Positionssensoren mit durchsichtigem Klebeband befestigt, um in
Hinblick auf die Bildaufnahme eine möglichst geringe Verfälschung zu erreichen. Um aus diesen Sensoren konkrete Positionen
der Fingergelenke zu berechnen, musste zusätzlich die Hand vermessen werden. Diese notwendigen Vorbereitungen
benötigten einen nicht erwarteten großen Anteil der Zeit. Bei diesem Vorgehen wurde der Ansatz aus \cite{yuan_bighand2.2m_2017} verfolgt.

Die Analyse der aufgenommenen Testdaten ergab, dass die Genauigkeit der bestimmten Positionen für diesen Anwenundungsfall
nicht ausreichend war.
Aufgrund dessen wurde der Entschluss gefasst, das Aufnahmesetup zu einem iPhone 13 Pro, iPad 11 Gen 2 und einer ZED 2i Stereokamera zu ändern. Das iPhone zeichnete Aufnahmen im Kino-Modus auf, um Zugriff auf die
Tiefendaten zu erhalten, beim iPad war ein solcher Kameramodus nicht vorhanden. Mangels einer programmierbaren Schnittstelle mussten die einzelnen Aufnahmen auf iPhone und iPad händisch gestartet und gestoppt werden,
die Stereokamera wurde über ein Python Skript gesteuert. Um den manuellen Start-/ Stoppaufwand gering und den Aufnahmeablauf möglichst effizient zu halten, wurde in einem Eck des Aufnahmeberechs ein Monitor platziert,
auf dem man zwischen zwei Farben umschalten konnte. Dem ging die Überlegung voraus, während der Gesten eine Farbe zu zeigen und in den Pausen zwischen den Gesten eine andere. Nach den Aufnahmen konnten so die Gesten
durch Unterscheidung der aktuellen Farbe auf dem Monitor automatisch in einzelne Dateien unterteilt werden. Da während den Aufnahmen der Ablauf der Gesten festgehalten wurde, konnte so auch ein semi-automatisches
Labeling stattfinden.

Nach Auswertung dieser Daten wurde festgestellt, dass dieser Aufnahmeprozess zu anfällig für menschliche Fehler war und das Setup wurde 
final auf zwei ZED 2i-Stereokameras angepasst. Diese konnten über ein Python Skript automatisiert
eine Abfolge von Gesten in einzelnen Dateien speichern. Pro Kamera wurde eine svo-Datei gespeichert, aus welcher zum einen Tiefendaten für jeden Pixel und zum anderen RGB-Bilder für jede der zwei Linsen ausgelesen werden konnten.
Diese Entscheidung erfolgte zum einen aufgrund der Vereinfachung des Aufnahmeablaufs zum anderen um zuerst nur mit RGB-Daten arbeiten zu können und bei Bedarf auf Tiefendaten zurückgreifen zu können.


\paragraph*{Datenauswertung} \mbox{}\\
Die Datenauswertung erstreckte sich über mehrere Aufnahmesetups und jedes brachte seine eigenen Herausforderungen 
mit sich. Die Ziele änderten sich während des Auswertungsprozesses und es wurde deutlich, dass die mangelnde Expertise 
in Gebärdensprache die Qualitätskontrolle erschwerte. Zum Ende hin wurde es auch schwer festzustellen, ob die Daten 
von einem gewissen Bias beeinflusst wurden, da vier Nicht-Experten als Akteure agierten und einige Gesten nicht 
ausreichend variierten.

Im ersten Setup lag der Fokus auf der Datenerzeugung und der Kalibrierung. Hierbei stellte sich heraus, dass die 
Kalibrierung ein sehr zeitaufwendiger Prozess war, der bei jeder Bewegung des Transmitters erneut durchgeführt 
werden musste. Zudem war die Genauigkeit des Transmitters nur über kurze Distanzen ausreichend präzise. Die Entwicklung 
eines virtuellen Handmodells zur Extraktion der Fingerpositionen aus den Daten der sechs Sensoren war eine zusätzliche 
Herausforderung. Da zu diesem Zeitpunkt geplant war, nur Aufnahmen mit Gebärdensprache Spezialisten durchzuführen, war es 
unser Ziel, den Aufwand für die Vorbereitung des Setups möglichst gering zu halten, um die Zeit der Experten nicht zu 
verschwenden. Leider stellte sich heraus, dass dies mit dem NDI-System nicht umsetzbar war. Dennoch konnten wir 
feststellen, dass wir Daten mit Apple-Geräten aufnehmen konnten, die für unsere Arbeit geeignet waren.

Im zweiten Setup wurde deutlich, dass die gewählte Konfiguration anfällig für Fehler war, insbesondere durch 
Fehler beim Labeln der einzelnen Gesten während der Aufnahme. Ein einziger Fehler zu Beginn erforderte die manuelle Neukennzeichnung 
des gesamten Videos. Um die Datenqualität nachträglich zu verbessern, wurde versucht, Fehler während der 
Aufnahme in einer Datei zu dokumentieren und später automatisch zu erkennen. Dies erwies sich jedoch als unpraktisch.

Das dritte und letzte Aufnahmesystem erwies sich als äußerst effizient in der Erzeugung qualitativ hochwertiger Daten. 
Diese Daten zeichneten sich durch eine hohe Qualität und Konsistenz aus. Wir nahmen 28 verschiedene Klassen mit 
jeweils 240 Videosegmenten auf und verteilten die Aufnahmen gleichmäßig, um Verzerrungen zu vermeiden. Dies bildete 
eine solide Grundlage für weitere Analysen und Modelle.

\paragraph*{Datenpreprocessing} \mbox{}\\
Der Ausgangspunkt der Daten war ein MP4-Ordner, welcher für jede Geste einen nach der Geste benannten Ordner enthielt. In jedem dieser Ordner befanden sich mp4-Dateien, auf welchen die jeweilige Geste ausgeführt wurde. Um diese Dateien nun in ein Format umzuwandeln, mit welchem das LSTM-Modell trainiert werden konnte, wurde ein NPY-Ordner erstellt, welcher wiederum für jede Geste einen Ordner enthielt. Indem durch jede mp4-Datei frameweise durchiteriert und jeder Frame mit Hilfe des Mediapipe Holistic Modells in einen Vektor aus Floats, welche die normalisierten Koordinaten der erkannten Keypoints darstellten, umgewandelt wurde, erhielt man für jede Videodatei eine 2-dimensionale npy-Datei, wobei hier jede Zeile dem resultierenden Vektor eines Frames entsprach.
Da jeder Frame einzeln vom Mediapipe Holistic Modell verarbeitet werden musste, traten schon bei kleineren Datensätzen hohe Laufzeiten auf. Aus diesem Grund wurde der Verarbeitungsprozess der mp4-Dateien aus dem Jupyter Notebook, in welchem anfangs noch der gesamte Erstellungsprozess des LSTM-Modells enthalten war, herausgenommen und in einer einzelnen Python-Datei ("`save\_keypoints.py"') parallelisiert implementiert.

\paragraph*{Modell entwickeln und testen} \mbox{}\\
\begin{figure}[H]
    \centering
    %\includegraphics[scale=0.4]{K5_Verlauf/diagrammLSTM.jpg}
    \caption{Aufbau und Funktionsweise LSTM-Modell}
\end{figure}
Als Grundlage für das zu entwickelnde Modell wurde der Ansatz aus \cite{nicholas_renotte_sign_2021} verwendet. Dabei werden die extrahierten Keypoints aus den Bildern in das Modell geladen, um dann mittels eines Modells aus drei LSTM Schichten, drei Dense Schichten und dem abschließenden Softmax einer Gebärde zu Text zu übersetzen. Um die verwendete Performancemetrik und den Trainingsprozess zu optimieren, wurden die Hyperparameter angepasst, Aktivierungsfunktionen geändert und eine orthogonale Initialisierung der Gewichte eingesetzt. Um zu verhindern, dass das Training des Modells vorzeitig abbricht, wurde zudem eine Learningrate Schedule eingesetzt. Mit diesen Anpassungen konnte eine Accuracy von 0,9 erreicht werden. 

\subsection{Entwicklung Demo-Applikation}
Zu Demonstrations- und Testzwecken des trainierten Modells wurde eine Desktop GUI Anwendung entwickelt. Diese wurde zunächst mit der tkinter Bibliothek implementiert, jedoch wechselte man später zum PySide6 Framework, da dieses eine schärfere Grafik und eine bessere Laufzeit hat. Die Anwendung wurde modularisiert auf zwei Dateien, wobei die Datei main.py vor allem die Logik für die GUI Anwendung und die Datei mediapipe\_api.py Funktionen zur Erkennung und dem Anzeigen der Keypoints enthält.

\subsection{CorrNet}
Bei dem in \cite{hu2023continuous} vorgestellten Model handelt es sich um ein Korrelationsnetzwerk (CorrNet) bestehend aus einem CNN, das ein Korrelationsmodul und ein Identifikationsmodul enthält. Es erfasst mittels der Berechnung von Korrelationen zwischen benachbarten Frames in den Sequenzen bildübergreifende Bewegungen und kann so Gebärden identifizieren. Das Modell wurde auf dem RWTH-PHOENIX Weather 2014-T Datensatz trainiert, welcher aus 386 transkripierten Wetterberichten besteht. Die Sequenzen wurden mit 25 Frames pro Sekunde aufgenommen wobei die Größe der Frames 210x260 Pixel beträgt. Der Datensatz enthält 1081 verschiedene Gebärden, in etwa 7000 Sätzen.
\begin{figure}[H]
    \centering
    %\includegraphics[scale=0.4]{K5_Verlauf/diagrammCNN.jpg}
    \caption{Aufbau und Funktionsweise Correlation Network}
\end{figure}
\paragraph*{CorrNet aufsetzen und integrieren} \mbox{}\\
Beim Einrichten von CorrNet war unser Ansatz, uns eng an den vorhandenen Code von CorrNet anzulehnen 
und so wenig wie möglich Anpassungen vorzunehmen. Dies erforderte zunächst eine gründliche Einarbeitung 
in die Struktur des Repositorys.

Die Komplexität des Repositorys wurde unterschätzt, daher war eine intensive Teamarbeit erforderlich, 
um die passende Lösung für unsere Anforderungen zu finden. Zuerst haben wir die Conda-Umgebung gemäß den 
Repository-Beschreibungen eingerichtet und den Datensatz auf die K017-Maschine heruntergeladen.

Dabei stießen wir auf ein Problem in der Datenstruktur, dass das weitere Ausführen der Anleitung blockierte. 
Der heruntergeladene Datensatz war nicht so strukturiert, wie es das CorrNet erwartet.
Das Problem konnte mithilfe des Skripts \path{unused/fix_phoenix_dataset.py} behoben werden.

Als nächstes galt es, den Inference-Mechanismus aufzurufen und in unsere Demo-Applikation zu integrieren. 
Dies stellte sich als die anspruchsvollste Aufgabe heraus, da sie ein umfassendes Verständnis des gesamten 
Projekts erforderte. Hier musste das ganze Team nahe zusammenarbeiten, um die Schnittstelle für die Demo
bereitzustellen.

\paragraph*{Demo anpassen} \mbox{}\\
Um mit der Desktop-Anwendung auch Predictions mit CorrNet durchzuführen, wurden weitere Funktionalitäten hinzugefügt. Die Funktionalitäten aus der vorherigen Version der Desktop-Anwendung blieben unverändert, jedoch konnte man nun zwischen zwei Ansichten mittels eines Buttons hin- und herwechseln, um entweder mit dem LSTM-Modell oder mit CorrNet zu arbeiten. Außerdem wurde eine weitere Datei corrnet\_api.py hinzugefügt, welche die Logik für die Prediction mittels CorrNet enthielt.

\paragraph*{CorrNet finetunen} \mbox{}\\
Als Ausgangslage für das Finetunen auf den aufgezeichneten Gesten wurde das ursprüngliche Trainingsskript und
Datenstrukur des CorrNets \cite{hu2023continuous} verwendet. Hierzu mussten in einem ersten Schritt die selbst
aufgezeichneten Daten in eine den Phoenix-Daten \cite{koller_continuous_2015} zumindest ähnliche Struktur
gebracht werden. Die Phoenix Daten waren in drei Ordner für Train, Test und Validation unterteilt. In diesen Ordnern
gab es für jede aufgezeichnete Sequenz einen Unterordner, welcher die Einzelbilder enthielt. Bei den selbst
aufgezeichneten Daten war auch bereits eine Unterteilung in Train, Test und Validation Ordner vorgenommen worden,
dort waren die Unterordner dagegen nach Label unterteilt, welche dann die Unterordner für die Einzelbilder enthielten.
Diese Struktur konnte allerdings beibehalten werden, da die Daten nicht über die Datenstruktur selbst, sondern über
npy-Dateien eingelesen werden, welche Informationen zu Pfad, Label und weiteren Metadaten enthielten.

Diese Informationsdateien wurden im nächsten Schritt generiert. Hierbei wurde über die Daten iteriert, diese auf
eine dem Phoenix-Datensatzes ähnliche Auf\-lösung runterskaliert und die entsprechenden Metadateien generiert. Diese Dateien
umfassen zum einen die npy-Dateien, welche Infos zu Speicherort, Label und weitere Infos der einzelnen Videos enthalten. Zum anderen die
stm-Dateien welche den einzelnen Videos Label zuweisen und vor allem in Hinblick auf mehrere Wörter pro Sequenz
wichtig sind. Die letzte generierte Datei war gloss\_dict.npy, welche die Häufigkeiten der einzelnen Labels enthält.

Im nächsten Schritt wurde das Modell entsprechend den bereits existierenden Skripts mit den bereits vortrainierten
Gewichten des Phoenix-Datensatzes geladen und die einzelnen Layer eingefroren. Anschließend wurden die Layer, welche
die Anzahl der Klassen enthielten neu angelegt. Für das Training musste das Trainingsskript leicht angepasst werden,
um trotz der Unterschiede in den Pfaden zwischen Phoenix und den eigenen Daten zu funktionieren.

Aufgrund der stark verringerten Anzahl der Gesten und Wörter pro Sequenz erreichte das auf den selbst aufgenommenen Daten gefinetunte
Modell einen stark verbesserten WER-Score von 5,4 \% im Vergleich zu 20,5 \% auf den Phoenix Daten.

\paragraph*{Vergleich: CorrNet vs. LSTM} \mbox{}\\
Durch die Anpassungen der Hyperparameter des entwickelten LSTM Modells konnte eine Accuracy von 0,9 erreicht werden. Jedoch konnte das Modell in der Demo-Applikation nicht die gewünschten Ergebnisse erzielen, vor allem bei statischen Gebärden. Zudem kann das Modell keine ganzen Sequenzen von Gebärden bestimmen, dazu hätten die Videos auf einzelne Gebärden separiert werden müssen, um danach die einzelnen Gebärden zu bestimmen und sie dann wieder zu einem Satz zusammenzufügen. Daraus folgte, dass das Modell langfristig in keiner mobilen App hätte eingebunden werden können und so das langfristige Projektziel nicht erreicht werden kann. Das vortrainierte Korrelationsnetzwerk konnte nach dem Finetunen mit unserem Datensatz einen WER-Score von 5,4 \% erreichen. Die Accuracy und der WER-Score können in diesem Zusammenhang nicht als Metrik für den Vergleich der beiden Modelle verwendet werden, jedoch unterscheiden sich die Modelle größtenteils in der Architektur und dem damit verbundenen Aufwand. Um mit dem LSTM ganze Sequenzen von Gebärden bestimmen zu können, wäre nicht nur ein zusätzliches Modell für die Verarbeitung von statischen Gebärden nötig, zusätzlich würde für die Vorbereitung der Daten und schließlich auch die Inferenz eine längere Laufzeit benötigt werden als für die Inferenz mit dem CorrNet. Die erheblich bessere Performance und der geringere Entwicklungsaufwand sprechen daher für das CorrNet und gegen das LSTM-Modell.

\section{Aufgetretene Herausforderungen}
Bei der Durchführung des Projekts sind sowohl organisatorische als auch technische Herausforderungen aufgetreten.
So war die Organisation der Termine für das Aufnehmen mit den dafür geplanten Gebärdensprache Spezialisten
langwieriger als geplant und beanspruchte mehr Zeit als zuerst vorgesehen. Bei den Testaufnahmen mit
diesen wurde dann festgestellt, dass auch die Aufnahmen selbst wesentlich länger dauerten als geschätzt. Hier 
hätte bereits im Vorfeld eine genauere Kommunikation bezüglich des Aufnahmeablaufs und der Durchführung stattfinden müssen, um allen Beteiligten zu ermöglichen sich darauf einzustellen. 

Zu Beginn des Projekts wurden die Aufgaben dokumentiert und zugeteilt, jedoch wurde diese Einteilung aus verschiedenen 
Gründen nicht immer eingehalten. Eine verbindlichere Rollenverteilung am Anfang wäre hilfreich gewesen. Während der Entwicklung
mussten verschiedene Ansätze ausprobiert werden, da zu Beginn weniger Zeit auf eine tiefgreifende Recherche investiert wurde und stattdessen schnell das Entwickeln begonnen wurde. 

So waren die Daten der Fingertrackingsensoren aufgrund der Genauigkeit nicht brauchbar und das automatisierte Schneiden der
Aufnahmen in einzelne Gesten funktionierte nur bedingt und nicht mit der gewünschten Genauigkeit. Wegen der fehlenden Recherche
war die Qualität der ersten Datenansätze nicht ausreichend, was zu falschen Einschätzungen der Modelle führte. So wurde das 
erste Modell als unbrauchbar eingeschätzt, aber mit besseren Daten konnte es doch verwertbare Ergebnisse aufweisen. 

Um möglichst viele Daten in kurzer Zeit aufzunehmen, wurde versucht, mit drei Stereokameras gleichzeitig aufzunehmen.
Allerdings wurde die Qualität der Aufnahmen durch die nicht ausreichende Bandbreite des USB Controllers verringert, weshalb
die Anzahl der Kameras reduziert wurde.

Beim Trainieren des Keypoint Modells traten ab einem gewissen Punkt Abstürze in der Accuracy für Train und Validation Datensatz auf.
Dies wurde durch Anpassen der Aktivierungsfunktion von ReLU zu tanh behoben.

Die Demo Applikation hatte zuerst eine schlechte Performance,  was der Anbindung der Kamera über OpenCV geschuldet war. Durch Setzen
des Codecs zu MJPG wurde die Performance deutlich verbessert.
