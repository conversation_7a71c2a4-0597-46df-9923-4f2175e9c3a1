\chapter{Ergebnis}
Das Ergebnis dieses Studienprojekts kann in Software und Hardware aufgeteilt werden.

Hardwareseitig wurde ein Aufnahmesetup bestehend aus zwei ZED 2i-Stereo\-kameras gefunden, welches sich eignet
um Gebärdensprache aufzunehmen. Dazu wurde ein Aufnahmeskript entwickelt, welches eine parallele Verwendung
der Kameras ermöglicht und den Aufnahmevorgang automatisiert, um menschliche Fehler zu minimieren und den Ablauf zu erleichtern.
Zudem wurde eine Checkliste erstellt, welche den Aufbau des Setups beschreibt und möglichen Fehlern beim Aufnehmen vorbeugen soll.

\begin{figure}[H]
    \centering
    %\includegraphics[scale=0.118]{K6_Ergebnis/IMG_20230829_120238.jpg}
    \caption{Aufbau Aufnahmesetup}
\end{figure}

Die aus den Aufnahmen resultierenden svo-Dateien wurden durch ein Skript in Videos konvertiert, aus welchen 
zum einen die Keypoints mittels Anbindung der Mediapipe-Bibliothek und zum anderen die Einzelbilder 
extrahiert wurden. Diese Skripte erstellten die für das Training notwendige Ordnerstruktur mit Unterteilung 
in train, test, und validation-Ordner.

Aus diesen Skripten entstand ein Datensatz für ausgewählte Gebärdengesten. Dieser besteht aus svo-Daten, welche 
Bild und Tiefeninformationen enthalten, Videos, Einzelbildern und extrahierten Keypoints. Der Datensatz 
ist auf Rechner WS03 gespeichert und besteht aus folgender Ordnerstruktur.

\begin{figure}[H]    
\dirtree{%
.1 /home/<USER>/Documents/data\_recordings/.
.2 all\_data/ \DTcomment{Die final verwendeten Daten, basierend auf Setup 3}.
.3 256x256px/ \DTcomment{Skalierte Bilder, veränderte aspect ratio, nicht verwendet}.
.4 <data split>.
.5 <classes>.
.6 <takes>.
.3 480x270px/ \DTcomment{Skalierte Bilder, originale aspect ratio, für finetuning}.
.4 <data split>.
.5 <classes>.
.6 <takes>.
.3 imgData/ \DTcomment{Einzelbilder jedes Takes als jpg}.
.4 <data split>.
.5 <classes>.
.6 <takes>.
.3 mp4/<classes>/ \DTcomment{avi Dateien für jeden Take}.
.3 NPY\_data/<classes>/ \DTcomment{Mediapipe keypoints für jeden take}.
.3 recordings\_overview.md \DTcomment{Info Datei über Anzahl Gesten}.
.2 aufnahmen\_setup1\_NDI/.
.3 fingertips \DTcomment{Aufgenommene Fingerpositionen}.
.3 hand-pose-estimation \DTcomment{Software zu Fingertracking}. 
.3 svo \DTcomment{Aufgenommene Stereokameras}.
.2 aufnahmen\_setup2\_apple/.
.3 preprocessed\_data/ \DTcomment{Für Segmentierung vorbereitete Daten}.
.3 raw\_data/<sensor>/ \DTcomment{Rohe Aufnahmedaten}.
.3 segments\_jsons/ \DTcomment{Timestamps der einzelnen Gesten in JSON}.
.3 segments\_videos/ \DTcomment{Videos der einzelnen Gesten als mp4}.
.3 data\_index.json \DTcomment{Info Datei}.
.2 aufnahmen\_setup3\_ZED/.
.3 <recording\_session> \DTcomment{Rohdaten}.
}
\end{figure}

Es wurde ein LSTM-Modell entwickelt, welches mithilfe der Keypoints trainiert wurde und abschließend eine 
categorical accuracy von 0,9 aufweisen konnte. Um dieses Ergebnis weiter zu verbessern, wurde das Corrnet
Modell verwendet. Dieses wurde aufgesetzt, dokumentiert und mit den eigenen Daten auf einen WER-Score von 
5,4 \% gefinetuned.

Um diese Ergebnisse praktisch umzusetzen, wurde eine Demo-Applikation entwickelt. Diese kann die beiden 
Modelle auf Bilder, die durch eine Webcam aufgenommen wurden, anwenden und die erzielten Ergebnisse auf
anschauliche Weise anzeigen. 
\begin{figure}[H]
    %\hspace*{-80px}
    \centering
    %\includegraphics[scale=0.198]{K6_Ergebnis/screen_small.png}
    \caption{Screenshot Demo Applikation}
\end{figure}

Zuletzt wurde ein Vergleich zwischen den beiden Modellen über die Funktionsweise und Ergebniskennzahlen gezogen,
welcher zeigte, dass das CorrNet das robustere und effizientere Modell ist.