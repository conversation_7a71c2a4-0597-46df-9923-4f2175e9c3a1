\chapter{\mbox{<PERSON>do<PERSON><PERSON> <PERSON> Forster}}

\section{Datenaufnahme}
\subsection{Sensorenauswahl}
Der Auswahl der Sensoren gingen folgende theoretische Überlegungen voraus:

Das NDI-Trackingsystem, bestehend aus einem Transmitter und sechs Sensoren, bot exakte Positionsdaten, was zu 
einer sehr hohen Wiederverwendbarkeit des Datensatzes führt und das Trainieren eines Modells vereinfacht, wenn 
die Informationen nicht erst aus Bildern extrahiert werden müssen.
Dem stand ein sehr hoher Aufbau- und Vorbereitungsaufwand gegenüber.
Nach dem Aufbau mussten die Sensoren an Fingerspitzen und Handrücken befestigt, die Hand vermessen
und die Positionsdaten mit einer RGB-Kamera kalibriert werden. Um diese Daten dann verwenden zu können hätte 
erst noch ein Handmodell entwickelt werden müssen, welches aus den sechs Sensoren die Punkte aller Fingergelenke 
bestimmt hätte.

Mit iPhone 13 Pro und iPad 11 Gen 2 standen Sensoren zur Auswahl, die auch ein Anwender einer Gebärdenapp zur 
Verfügung hätte. Mit diesen Sensoren konnte man nicht nur RGB-Bilder aufnehmen, sondern auch Tiefendaten - sofern
man im Kinomodus aufzeichnete. Diese Tiefendaten waren jedoch nur auf kurze Distanz genau und lagen als 
Grauwertvideo vor, aus dem man nur einen relativen Abstand zwischen hellen und dunklen Bereichen und keine 
genauen Entfernungen rekonstruieren hätte können. Der Aufnahmeprozess war zudem nicht optimal, da die 
Aufnahmen nur manuell gestartet und nach Ende händisch über eine USB-Verbindung auf den Rechner übertragen werden
mussten. Für die Extrahierung der Tiefendaten aus den MOV-Dateien lagen zudem keine offiziellen Tools von Apple vor.

Die Verwendung einer Logitech c925e, welche nur RGB-Bilder liefert, konnte im Gegensatz zu obigen Sensoren 
schnell über OpenCV angebunden und verwendet werden. Die dabei aufgezeichneten Daten entsprachen dabei 
nicht der Datenquantität der anderen Sensoren, da bei jedem anderen zusätzlich zu RGB-Bilder andere Daten
wie Tiefen- oder Positiondaten entstehen. Durch die sehr einfache Verwendbarkeit aber wurde entschieden, die 
Webcam für Tests oder Demos verwenden zu können.

Die ZED 2i-Stereokameras konnten über USB an den PC angeschlossen werden und dort über eine gut dokumentierte
Python-API verwendet werden. Hierbei war auch ein Setup mit mehreren Stereokameras 
möglich. Die Python Schnittstelle bot eine einfache Möglichkeit Aufnahmen zu automatisieren. Sollte die 
Entscheidung getroffen werden, nicht auf den resultierenden Tiefendaten zu trainieren, stünde bei gleichem 
Trainingsaufwand die doppelte Menge an RGB-Bildern zur Verfügung da mit jeder Stereokameras zwei Videos mit
unterschiedlichen Winkeln aufgezeichnet wurden.

\subsection{Erstes Aufnahmesetup}
Mit dem NDI-System wurde als API eine dll-Datei und C++ Beispiele geliefert. Diese Schnittstelle wurde von 
bereits von C++ in Python übersetzt, hier wiederverwendet und weiter 
angepasst. So wurden die Funktionen aus \cite{yuan_bighand2.2m_2017} implementiert, um aus den Sensordaten die Positionsdaten aller 
Fingergelenke zu bestimmen, sowie diese Positionen auf ein RGB-Bild abzubilden. Des Weiteren wurde implementiert, 
dass aufgenommene Daten einer ZED 2i-Stereokamera und eines NDI-Systems anhand der aufgenommenen
Metadaten zeitlich aufeinander abgestimmt werden konnten. 

\subsection{Zweites Aufnahmesetup}
Das zweite Aufnahmesetup bestand aus Stereokamera, iPhone 13 Pro und iPad 11 Gen 2. Da in einem Video mehrere Gesten 
aufgenommen werden sollten, um den Aufnahmeaufwand gering zu halten, mussten diese voneinander unterscheidbar gemacht werden. Hierzu 
wurde zuerst damit experimentiert, das Gesicht mit einem roten Papier zu verdecken und dadurch das Ende einer Geste zu 
markieren. Hierzu wurde mithilfe des Moduls face-regocnition \cite{geitgey_face_2023} die Position eines Gesichts 
bestimmt um in den nächsten Bildern an dieser Stelle zu prüfen, wie hoch der Anteil im Bereich des Gesichts mit 
deutlichen Rotwerten ist. Diese Vorgehensweise hatte zum einen das Problem, dass die Gesichtserkennung, 
aufgrund des Inputs der originalen Bildgröße von 1920x1080, sehr langsam lief und das Verdecken des Gesichts 
durch den Gefilmten selbst zu einer Verfälschung der Gebärden führte, da die Ruheposition der Hände normalerweise 
auf Bauchhöhe ist.

Aus diesem Grund wurde das Verdecken des Gesichts durch Umschalten der Farbe auf einem Monitor im Bild abgeändert. Dies wurde 
durch eine HTML-Seite realisiert, auf der sich bei einer beliebigen Tasteneingabe die Hintergrundfarbe änderte. 
Das Umschalten erfolgte nicht mehr durch den Akteur selbst, sondern durch eine zweite Person. Da sich die Position des 
Monitors nicht veränderte konnte die Gesichtserkennung auf dem ganzen Bild auf eine Auswertung eines Bruchteils des Bildes 
reduziert werden. Hierfür musste nur für jede Kamera einmal die Position des Monitors auf den Aufnahmen und ein Farbraum für 
die Rotwerte mit Ober- und Untergrenzen festgelegt werden, da sich die Rotwerte zwischen den einzelnen Kameras unterschieden. 
Die Laufzeit konnte dadurch auf nahezu Echtzeit mit 30 Frames pro Sekunde erhöht werden. Anschließend wurde eine JSON Datei 
generiert, welche Index, Start- und Endframe sowie das Label enthielt. Mithilfe eines dafür geschriebenen Skripts konnte 
dieses JSON dafür verwendet werden, die Videos mithilfe von FFMPEG auf die einzelnen Gesten zuzuschneiden und zu 
speichern.

Um mit den aufgezeichneten Daten arbeiten zu können wurden weitere Programme benötigt. So wurde das Commandline Tool MP4Box verwendet,
um die Tiefendaten aus den MOV-Dateien zu extrahieren:
\begin{lstlisting}[language=Java, caption={Auszug unused/extract\_depth.sh}]
    MP4Box -add self#2:hdlr=vide INPUT_FILE.mov -out remux.mp4
    ffmpeg -vcodec hevc -i remux.mp4 -map 0:1 map.mp4
\end{lstlisting}
Der Output map.mp4 enthält ein Grauwert-Video der Auflösung 320x180 mit den Tiefendaten der Aufnahme. Durch Anpassung 
des Videostream-Parameters des MP4Box Befehls konnte so auch ein einzelner Videostream der Auflösung 1920x1080 mit den RGB-Werten extrahiert 
werden.

Für die Verarbeitung der SVO Dateien aus der Stereokamera gab es bereits Vorlagen aus dem Stereolabs Repository \cite{stereolabs_svo_export_2023}, 
welche aus den SVO Dateien avi Dateien exportierten. Um die Videos einfacher in den Ablauf des Modells zu integrieren wurden mithilfe
eines weiteren Skripts die einzelnen Bilder aus den Videos extrahiert und in einer entsprechenden Ordnerstruktur gespeichert.
\newpage
\subsection{Drittes Aufnahmesetup}
Aufgrund der in der anschließenden Datenanalyse festgestellten Ungenauigkeiten des Labelings in diesem Vorgang wurde beschlossen,
das Setup weiter anzupassen, um einen genau gesteuerten Aufnahmevorgang durchführen zu können. Dazu wurde auf einem Skript von Stereolabs 
aufgebaut \cite{noauthor_stereolabs_2023} welches die Grundfunktionaliät eines Multi-Kamera Setups implementierte. Es wurde 
ein Aufnahmeablauf programmiert, für welchen man zu Beginn Inputs, wie Bezeichner des Akteurs oder Name der Geste, für die 
Bennenung der Dateien eingeben muss. Anschließend zeichnet das Skript nach einem Countdown mit allen Kameras gleichzeitig die 
Geste auf und speichert sie. Diese Aufzeichnung wird einem gegebenen Wert entsprechend oft wiederholt. Um die Aufnahmen möglichst 
synchron zu halten, musste das Skript weiter optimiert werden. So wurde eingebaut, nach Abschluss einer Aufnahme auf einer Kamera 
erst auf die Fertigstellung auf allen anderen Kameras zu warten, bevor der nächste Durchgang ausgeführt wurde. Bei Testaufnahmen 
wurde festgestellt, dass bei drei Stereokameras mit je 720p und 30 fps die Bandbreite des USB-Controllers nicht ausreichte und es 
zu Verzögerungen in den Aufnahmen kam. Zur Vermeidung dieser Datenverfälschung wurde das Setup auf zwei Stereokameras begrenzt.

Im Zuge der Aufnahmen entstand auch eine Checkliste, um die Aufnahmen soweit wie möglich zu vereinheitlichen, reproduzierbar und 
weniger fehleranfällig zu machen und das Vergessen wichtiger Punkte auszuschließen.

Das Resultat jeder Aufnahme waren somit zwei SVO-Dateien, daraus extrahiert insgesamt vier Videos im avi Format, aus welchen
die Einzelbilder extrahierten wurden. Das Extrahieren der Bilder erfolgte dabei durch ein Skript, welches gleichzeitig
die Unterteilung in Train-, Test- und Validationset durchführt. Diese Sets konnten im Datenpreprocessing dann für das 
Modell weiter vorbereitet werden.

\section{Finetuning CorrNet}
Für das Finetunen von CorrNet mithilfe der selbst aufgenommenen Daten wurde grundlegend der Ansatz verfolgt,
so viel wie möglich an bestehenden Code von CorrNet wiederzuverwenden und möglichst wenig Anpassungen zu machen.
Dazu war eine genaue Kenntnis des Aufbaus und der Funktionsweise des Codes nötig, wobei hier das bereits beim
Aufsetzen erworbene Wissen wiederverwendet werden konnte. 

Zuerst mussten die Daten in das selbe Format wie der Phoenix Datensatz, mit welchem CorrNet ursprünglich trainiert wurde, gebracht
werden. Dazu wurden die einzelnen Bilder auf eine Auflösung von 480x270px runterskaliert. Dadurch wurden beim zentrierten Croppen der 
Bilder auf 256x256px beim Laden in das Modell hauptsächlich relevante Informationen verwendet. Die Datenstruktur des 
Phoenix Datensatzes war entsprechend der Trainingsphasen in train-, test- und validation-Ordner unterteilt. 
In diesen waren die Bilder der einzelnen Sequenzen jeweils in einem Unterordner gespeichert. Bei den selbst 
aufgenommenen Daten war bereits dieselbe Unterteilung in Phasen vorhanden, jedoch wurde in diesen Ordnern 
weiter nach Label sortiert. Diese Struktur konnte allerdings beibehalten werden, da die Daten mithilfe von 
npy Dateien in das Modell geladen wurden, welche Informationen zu den einzelnen Sequenzen enthielten.

Diese Informationsdateien mussten im nächsten Schritt generiert werden. Es wurde für jede Trainingsphase 
eine npy Datei, welche den Pfad zu den Sequenzen, Label und weitere Metadaten enthielt, sowie eine phasenübergreifende 
npy-Datei mit allen Labeln und deren Häufigkeiten angelegt. Für die einzelnen Phasen wurden des 
weiteren stm-Dateien angelegt, welche für jede Sequenz die entsprechenden Label enthielten und für die 
Berechnung des WER-Scores benötigt wurden. Die Generierung dieser Dateien wurde im gleichen Notebook
wie das Runterskalieren der Bilder implementiert und konnte dadurch in einem Schritt durchgeführt werden.

Anschließend musste das Modell an die neue Klassenanzahl angepasst werden. Dabei wurde zuerst versucht die 
Module des CorrNets über Hinzufügen des CorrNet-Pfads zu \verb|sys.path| einzubinden. Um weitere Abhängigkeiten, 
wie Konfigurationsdateien, zu vereinfachen wurde später entschieden, das Skript zum Ausführen 
in den CorrNet Ordner zu kopieren. Zum Anpassen des Modells wurde es entsprechend dem CorrNet-Code geladen 
und anschließend alle Parameter eingefroren. Darauf folgte das Ersetzen aller, mit der Anzahl der
Klassen initialisierten, Layer mit angepassten Layern.

\begin{lstlisting}[caption={Auszug 07\_finetune/finetune.py}]
    def replace_layers_with_new_class_num(model, num_classes, gloss_dict):
        c1d_old = model.conv1d
        decoder_old = model.decoder
        classifier_old = model.classifier

        model.conv1d = TemporalConv(c1d_old.input_size, c1d_old.hidden_size, c1d_old.conv_type, c1d_old.use_bn, num_classes)
        model.decoder = Decode(gloss_dict, num_classes, decoder_old.search_mode)      
        
        if isinstance(c1d_old.fc, NormLinear):
            model.classifier = NormLinear(c1d_old.hidden_size, num_classes)
            model.conv1d.fc = NormLinear(c1d_old.hidden_size, num_classes)
        else:
            model.classifier = nn.Linear(c1d_old.hidden_size, num_classes)
            model.conv1d.fc = nn.Linear(c1d_old.hidden_size, num_classes)
        if c1d_old.fc is classifier_old:
            model.conv1d.fc = model.classifier
\end{lstlisting}

Nach diesen Anpassungen konnte für das neue Modell ein Adam Optimizer angelegt werden.

Vor Starten des Trainings mussten nur noch die Konfigurationsdateien angepasst werden. In der Datei \verb|baseline.yaml|
wurde \verb|dataset: finetune| gestetzt und eine entsprechende Datei \verb|finetune.yaml| mit Pfaden zu Datensatz
und Evaluations-Ordner angelegt. Die generierten npy Dateien wurden in \verb|preprocess/finetune| abgelegt, 
\verb|gloss_dict.npy| in \verb|dataset| und die stm Dateien in den Evaluationsordner geschoben. Bei allen Dateinamen
wurde "`val"' in "`dev"' umbenannt, um CorrNets Bezeichnung zu entsprechen.

Das Training konnte anschließend über den Aufruf \verb|python finetune.py| gestartet werden. Im ersten Training für
40 Epochen wurde bei Epoche 37 der Bestwert des WER-Score von 5,4\% erreicht. Bei einem zweiten Training mit 100
Epochen konnte dieser Wert nicht übertroffen werden.