\chapter{Individualdokumentation - Breu}

\section{Keypoint Modell}

\subsection{Testprogramme für Mediapipe und Extrahierung von Keypoints}
Nachdem im Team entschieden worden war, dass der im Youtube-Tutorial aufge\-führte LSTM-Ansatz \cite{nicholas_renotte_sign_2021} weiter verfolgt werden sollte, mussten zunächst zwei kleine Testskripte entworfen werden, um grundlegende Funktionalitäten der Pose Detection mittels Mediapipe zu prüfen.

Beim ersten Testprogramm ("`unused/test\_mediapipe.py"') handelte es sich um ein Skript, mit welchem man mittels Webcam oder externer Kamera live die von Mediapipe erkannten Keypoints auf dem Bildschirm anzeigen lassen konnte.

Mit dem zweiten Testprogramm ("`unused/keypoints\_to\_csv.py"') wurden die Keypoints der einzelnen Frames einer mp4-Datei extrahiert und anschließend in einer CSV-Datei gespeichert, wobei hier jede Zeile die Keypoints von einem Frame beinhaltete.

\subsection{Erste Version des LSTM-Notebooks}
Nachdem die Testprogramme alle erfolgreich ausgeführt worden waren, konnte die erste Version des Jupyternotebooks für den LSTM-Ansatz geschrieben werden. Mit diesem konnte der gesamte Prozess bis zum fertigen Modell durchgeführt werden, sobald man einen Datensatz bestehend aus mp4-Dateien hatte. Hierzu musste man spezifizieren, wo der Ordner lag, welcher die Ordner mit den einzelnen Gesten enthielt. Diese Struktur enthielt für jede Geste einen Ordner, der nach dieser Geste benannt war und für jede Ausführung der Geste eine mp4-Datei enthielt. Das Notebook erkannte dann automatisch die einzelnen Labels (Gestennamen) und erstellte entsprechende Ordner, in welchen später die CSV-Dateien gespeichert werden sollten. Im nächsten Schritt wurden dann, ähnlich wie im oben genannten Testprogramm zur Speicherung der Keypoints in CSV-Dateien, die Keypoints für alle mp4-Dateien von jeder Geste extrahiert und im entsprechenden Ordner gespeichert. Nachdem die CSV-Dateien lokal abgespeichert worden waren, wurden diese wiederum in das Notebook geladen als ein Array der Dimension (Anzahl Videos pro Geste * Anzahl Gesten, Frames pro Video, 1662 (Anzahl Keypoints pro Frame)). Da Mediapipe die Koordinaten der Keypoints automatisch normalisiert, mussten nur noch die Labels zu einer binären Klassenmatrix umgewandelt und anschließend in Train- und Test-Set gesplitet werden. Danach wurde ein Tensorboard Log erstellt, um Informationen über den Trainingsprozess des Modells zu erhalten. Anschließend wurde das LSTM-Modell implementiert, kompiliert und mit den Trainingsdaten trainiert. Zuletzt konnte man das Modell mit einer Multi-Label-Confusion-Matrix und dem Accuracy-Score auswerten und das fertige Modell am gewünschten Ort speichern.

\subsection{Testversuche mit verschiedenen Datensätzen}
Nachdem die erste funktionierende Version des LSTM-Notebooks erstellt worden war, wurde der erste Versuch gestartet, ein Modell mit den Daten, welche zusammen mit den Gebärdensprachexperten aufgenommen worden waren, zu trainieren. Jedoch stellte sich heraus, dass das Modell mit diesen Daten nichts lernen konnte. Aufgrund dessen wurden zwei Vermutungen zum gescheiterten Training aufgestellt: Entweder war das Modell/der Ansatz für die Aufgabenstellung ungeeignet oder die Daten waren nicht gut genug/zu wenig. In dem Tutorial, an welchem sich dieser Ansatz orientierte, war jedoch eine sehr hohe Accuracy bei einer ähnlichen Menge an verfügbaren Daten erreicht worden. Des Weiteren wurde die Qualität der Daten, welche zusammen mit den Studenten aus dem Gebärdensprachen-Studiengang aufgenommen worden waren, angezweifelt, denn zum Zeitpunkt der Aufnahmetermine war noch nicht klar gewesen, welcher Ansatz in der Zukunft verfolgt werden würde, somit waren die Daten auch nicht in einem für diesen Ansatz spezifischen Format aufgenommen worden. Um diese Spekulationen abschließend zu klären, wurden zwei Datensätze von jeweils 3 * 30 Videos aufgenommen, wobei der eine Datensatz Trainingsvideos zu den Gebärden "`A"', "`B"' und "`C"', welche alle statische Gesten sind, bei welchen kaum Bewegung erfolgt, und der andere Datensatz die Gebärden "`Hello"', "`I love you"' und "`Thanks"', welche im Tutorial verwendet worden waren und alle sehr dynamisch sind, enthielt. Bei der Aufnahme dieser Datensätze wurde speziell darauf geachtet, dass der Proband sich immer möglichst in der Mitte des Bildschirms positionierte und nur der Oberkörper gefilmt wurde, um nicht unnötig viele Informationen zu erfassen. Außerdem wurden die Gesten gleichmäßig und komplett über die genormte Länge des Videos ausgeführt. Es stellte sich heraus, dass das Modell mit dem dynamischen Datensatz eine Accuracy von über 90\% erreichte im Gegensatz zum statischen Datensatz, mit welchem kein Lernfortschritt beim Modell beobachtet werden konnte. Hieraus wurden folgende Erkenntnisse gewonnen:
\begin{itemize}
    \item Das Modell erkennt dynamische Gesten besser als statische
    \item Eine genormte Positionierung vor der Kamera ist wichtig
    \item Man muss darauf achten, dass die Geste möglichst sauber und vollständig ausgeführt wird
\end{itemize}

\subsection{Aufteilung des Notebooks}
Es fiel auf, dass der Vorgang der Extrahierung und Speicherung der Keypoints relativ zeitaufwendig war, obwohl bisher nur kleine Datensätze mit wenigen Gesten getestet worden waren. Somit musste eine effizientere Lösung gefunden werden, um auch größere Datensätze in einer vernünftigen Zeit verarbeiten zu können. Mit der multiprocessing Bibliothek sollte dieser Prozess parallelisiert werden, jedoch stellte sich heraus, dass \verb|multiprocessing.Pool| nicht mit JupyterNotebooks kompatibel ist \cite{dano_answer_2014}. Aus diesem Grund wurde beschlossen, dass die Extrahierung und Abspeicherung der Keypoints außerhalb des Notebooks in einer py-Datei stattfinden sollte und dann die fertigen Daten nur noch in das Notebook geladen werden müssten. Bei dieser Gelegenheit wurde auch das Dateiformat, in welchem die Koordinaten der Keypoints gespeichert wurden, von CSV zu NPY geändert, da dieses weniger Speicherplatz benötigten. Am Notebook wurden keine großen Veränderungen durchgeführt. Der Teil, welcher für die Extrahierung und Speicherung verantwortlich war, wurde aus dem Notebook entfernt und in eine py-Datei geschrieben. Die Parallelisierung konnte nun einfach und effizient mit ein paar Codezeilen umgesetzt werden, indem man der Funktion \verb|starmap| der Klasse \verb|Pool| aus der multiprocessing Bibliothek zum einen die Funktion übergab, welche parallel ausgeführt werden sollte, und zum anderen eine Liste von Parametern für diese Funktion. Die Funktion \verb|starmap| erstellte default-mäßig einen Process für jeden CPU-Core.

\section{Demo Applikation}
\subsection{Desktop Anwendung}
Zu Demonstrations- und Testzwecken des trainierten Modells wurde eine Desktop GUI Anwendung entwickelt. Diese wurde zunächst mit der "`tkinter"'-Bibliothek implementiert, jedoch wechselte man später zum "`PySide6"'-Framework, da dieses eine schärfere Grafik und eine bessere Laufzeit hat. Die Anwendung wurde modularisiert auf zwei Dateien, wobei die "`main.py"'-Datei vor allem die Logik für die GUI Anwendung und die "`mediapipe\_api.py"'-Datei Funktionen zur Erkennung und dem Anzeigen der Keypoints enthält.

Funktionalitäten der Anwendung:
\begin{itemize}
    \item "`Show Keypoints"'-Button: Anzeigen der von Mediapipe erkannten Keypoints in Echtzeit
    \item "`Start Recording"'-Button: Sobald Button geklickt wurde, werden 90 Frames aufgenommen und anschließend dem Modell übergeben
    \item Output-Label: Anzeigen von Anzahl der Frames bei Aufnahme und Prediction
    \item Image-Label: Anzeigen der von Kamera erfassten Frames auf der GUI
\end{itemize}

\subsection{Erweiterung der Desktop Anwendung mit CorrNet}
Um mit der Desktop-Anwendung auch Predictions mit CorrNet durchzuführen, wurden weitere Funktionalitäten hinzugefügt. Die Funktionalitäten aus der vorhe\-rigen Version der Desktop-Anwendung blieben unverändert, jedoch konnte man nun zwischen zwei Ansichten mittels eines Buttons hin- und herwechseln, um entweder mit dem LSTM-Modell oder mit CorrNet zu arbeiten. Außerdem wurde eine weitere Datei "`corrnet\_api.py"' hinzugefügt, welche die Logik für die Prediction mittels CorrNet enthielt.

Funktionalitäten der Anwendung:
\begin{itemize}
    \item "`Current: LSTM/CorrNet"'-Button: Wechseln zwischen LSTM- und Corrnet-Interface
    \item Interface, wenn LSTM ausgewählt ist:
    \begin{itemize}
        \item Siehe vorherige Version der Desktop-Anwendung
    \end{itemize}
    \item Interface, wenn CorrNet ausgewählt ist:
    \begin{itemize}
        \item "`Start/Stop Recording"'-Button: Durch Klicken des Buttons wird Aufnahme der Frames manuell gestartet/gestoppt und diese werden anschließend dem Modell übergeben
        \item Output-Label: Anzeigen von Anzahl der Frames bei Aufnahme und Prediction
        \item Image-Label: Anzeigen der von Kamera erfassten Frames auf der GUI
    \end{itemize}
\end{itemize}
