\chapter{\mbox{Individualdokumentation - Brunner}}

\section{Datenauswertung}
Die Datenauswertung erstreckte sich über verschiedene Aufnahmesetups, von denen jedes 
seine eigenen Herausforderungen und Einsichten mit sich brachte. Die Ziele für jedes 
dieser Setups änderten sich bei jeder Auswertung. In den frühen Phasen der 
Datenauswertung wurde klar, dass die fehlende Expertise in der Gebärdensprache die 
allgemeine Qualitätsprüfung erschwerte. Zum Schluss hin war es sogar schwierig 
festzustellen, ob die Daten einen gewissen Bias aufwiesen, da vier Nicht-Experten 
als Akteure agierten. Es gab auch Situationen, in denen die Diversität fehlte 
und die einzelnen Gesten boten für einen Keypoint-Ansatz nicht genügend Variation.

\subsection{Erstes Aufnahmesetup}
Im ersten Aufnahmesetup lag der Schwerpunkt weniger auf der eigentlichen 
Datenauswertung, sondern viel<PERSON>hr darauf, die Eignung des Aufbaus zur Erzeugung 
brauchbarer Daten zu überprüfen. Dabei wurde festgestellt, dass die Kalibrierung 
ein äußerst zeitaufwendiger Prozess ist, der bei jeder Bewegung des 
Transmitters erneut durchgeführt werden musste. Zusätzlich stellte sich heraus, 
dass die Genauigkeit des Transmitters nur über kurze Entfernungen ausreichend präzise 
war. Falls der Ansatz weiterverfolgt worden wäre, hätte auch noch ein virtuelles Handmodell 
entwickelt werden müssen, um die Fingerpositionen aus den Daten der sechs Sensoren zu extrahieren. 

Zu diesem Zeitpunkt war geplant, nur Aufnahmen mit Gebärdensprachexperten durchzuführen, 
daher war es unser Ziel, den Aufwand für die Vorbereitung des Setups so gering wie 
möglich zu halten, um die Zeit der Experten nicht zu bean\-spruchen. Es stellte sich heraus,
dass dies mit dem NDI-System nicht umsetzbar war. Bei der Suche nach weniger aufwendigen Sensoren
wurde festgestellt, dass die ZED Kameras für das weitere Vorgehen geeignet war.

\subsection{Zweites Aufnahmesetup}
Im zweiten Aufnahmesetup offenbarte sich, dass die gewählte Konfiguration aufgrund ihrer
Anfälligkeit für Fehler nicht geeignet war. Insbesondere menschliche Fehler bei der Aufnahme 
führten zu Problemen. Ein einziger Fehler bei der Gestiku\-lierung zu Beginn erforderte die manuelle Neukennzeichnung 
des gesamten Video\-segments. Um die Datenqualität nachträglich zu verbessern, wurde versucht,
die Fehler während der Aufnahme in der Datei \path{data_index.json} zu dokumentieren. 
Diese Dokumentation wird im Jupyter Notebook \path{segment_videos.ipynb} dann aufgegriffen und 
ermöglicht, fehlerhafte Daten weitgehend automatisch zu erkennen und sie dann aber manuell zu 
entfernen. Die Umsetzung erwies sich jedoch als unpraktikabel, da es erforderlich wäre, alle drei 
Aufnahmen gleichzeitig manuell zu starten. Neben dem misslungenen Versuch, das Labeln halbautomatisiert zu 
gestalten erwies sich das Extrahieren der MOV-Datein als unerwartet zeitaufwendig.

\subsection{Drittes Aufnahmesetup}
Im dritten Aufbau erwies sich die finale Konfiguration und Kameraauswahl als äußerst effizient 
in der Erzeugung eines umfangreichen Datensatzes. Die Daten zeichnen sich durch eine hohe Qualität aus, 
da sie frei von Ausreißern und Inkonsistenzen in der Segmentlänge sind. Zudem sind sie übersichtlich 
strukturiert. Vor diesem Hintergrund entschieden wir uns, 28 verschiedene Klassen mit jeweils 240 
Videosegmenten aufzunehmen. Um Zeit zu sparen und 
den Arbeitsaufwand zu minimieren, wurde diese Aufgabe intern im Team übernommen. Jeder Akteur
hat gleich viele Instanzen zum Datensatz beigetragen, um mögliche Verzerrungen entgegenzuwirken

Um die Vielfalt der Daten zu erhöhen und ihre Repräsentativität zu gewährleisten,
wurden verschiedene Kamerawinkel und Akteure berücksichtigt. Obwohl die Idee eines 
Trainingskorpus aus ganzen Sätzen in Betracht gezogen wurde, entschieden wir uns 
letztendlich dafür, die ersten Modelle mit den 6720 Videosegmenten, bestehend aus einzelnen
Gesten zu trainieren. Dies ermöglichte es uns, eine solide Grundlage für weitere
Analysen und fundierte Schlussfolgerungen zu schaffen.

\newpage
\section{CorrNet aufsetzen}
Für das Aufsetzen des CorrNet wurde der grundlegende Ansatz verfolgt, sich möglichst 
nahe am bestehenden Code von CorrNet \cite{hu2023continuous} zu orientieren und so wenig wie möglich 
Anpassungen vorzunehmen. Dies erforderte zunächst eine gründliche Einarbeitung 
in die Struktur des Repositorys.

Die Komplexität des Ansatzes wurde unterschätzt, 
weshalb das Team eng zusammenarbeiten musste, um die für unsere Anforderungen 
geeignete Lösung zu finden. Als Erstes wurde die in den Repositorys beschriebene 
Conda-Umgebung eingerichtet und der Datensatz auf die Maschine im K017 
heruntergeladen. Dabei stellte sich heraus, dass die Videopfade in 
\path{/annotations/manual/*.csv} nicht mit den vorhandenen Pfaden in 
\path{/features/fullFrame-210x260px/} übereinstimmten, da das Verzeichnis 
\path{"1"} fehlte. Mithilfe von \path{unused/fix_phoenix_dataset.py} wur\-de das Problem gefixt.

Dieser Code hat die Aufgabe, Dateien in einem Verzeichnisbaum neu zu organisieren. 
Dabei werden alle Datein in Unterordner, in Unterunterordner \path{"1"} verschoben, falls nicht schon existiert.

\begin{lstlisting}[language=Python, caption={Auszug unused/fix\_phoenix\_dataset.py}]
    import os
    import shutil
    
    # must be repeated for dev, train and test folder
    dir_path = "<path_to_phoenix>.../fullFrame-210x260px/dev"
    subfolders = [x for x in os.listdir(dir_path) if os.path.isdir(os.path.join(dir_path, x))]
    
    for dir in subfolders:
        if "1" in os.listdir(os.path.join(dir_path, dir)):
            continue # 1 dir already exists, assume files are in correct dir
        files = [x for x in os.listdir(os.path.join(dir_path, dir)) if os.path.isfile(os.path.join(dir_path, dir, x))]
        goal_dir = os.path.join(dir_path, dir, "1")
        os.makedirs(goal_dir)
        for f in files:
            shutil.move(os.path.join(dir_path, dir, f), goal_dir)
\end{lstlisting}
\newpage

Der Ablauf des Codes ist wie folgt: Zunächst wird eine Liste (\texttt{subfolders}) 
aller Unterverzeichnisse im angegebenen Hauptverzeichnis erstellt. Anschließend 
wird eine Schleife gestartet, die jedes dieser Unterverzeichnisse durchgeht. In 
jedem Unterverzeichnis wird überprüft, ob bereits ein Unterverzeichnis mit dem 
Namen \path{"1"} existiert. Wenn ja, wird angenommen, dass die Dateien bereits im richtigen 
Verzeichnis liegen, und die Schleife wird für dieses Unterverzeichnis übersprungen. 
Wenn jedoch kein Verzeichnis \path{"1"} vorhanden ist, werden alle Dateien im aktuellen 
Unterverzeichnis aufgelistet. Es wird ein Zielverzeichnispfad (\texttt{goal\_dir}) erstellt 
und falls dieses Zielverzeichnis noch nicht existiert, wird es neu angelegt.
Schließlich werden die Dateien aus dem aktuellen Unterverzeichnis in das 
Zielverzeichnis \path{"1"} verschoben. Dieser Vorgang wird für jedes Unterverzeichnis 
wiederholt, um sicherzu\-stellen, dass die Dateien ordnungsgemäß organisiert sind. Die 
restliche Anleitung konnte ohne größere Probleme wie beschrieben abgearbeitet werden.

\section{CorrNet API}
Im nächsten Schritt galt es herauszufinden, wie der Inferenz-Mechanismus aufge\-rufen 
und in unsere Demo-Applikation integriert werden konnte. Dies erwies sich als die 
schwierigste Aufgabe, da hier eine tiefgehende Kenntnis des gesamten Projekts erforderlich 
war. Schritt für Schritt wurde ein neues Skript entwickelt.

\begin{lstlisting}[language=Python, caption={Auszug 05\_demo/corrnet\_api.py}]
def seq_eval(model:slr_network.SLRModel, img_list:List[np.ndarray])->Dict:
    model.eval()
    
    input_data = [cv2.cvtColor(img, cv2.COLOR_BGR2RGB) for img in img_list]
    data_aug = video_augmentation.Compose([
            video_augmentation.CenterCrop(224),
            video_augmentation.Resize(1.0),
            video_augmentation.ToTensor(),
        ])
    video, _ = data_aug(input_data, [])
    video = video[None, :, :, :, :]
    video = video.float() / 127.5 - 1
    video = video.to(device.output_device)
    video_lgt = torch.from_numpy(np.array([len(input_data)]))
    video_lgt = video_lgt.to(device.output_device)

    with torch.no_grad():
        ret_dict = model(video, video_lgt)

    return ret_dict
\end{lstlisting}

Die Funktion \texttt{seq\_eval} nimmt zwei Parameter entgegen: \texttt{model}, 
welches ein CorrNet Modell (vom Typ \texttt{slr\_network.SLRModel} ist) 
und \texttt{image\_list}, eine Liste von Bildern als NumPy-Arrays.

Zu Beginn wird das Modell in den Evaluierungsmodus versetzt, wodurch verhindert 
wird, dass es während der Ausführung der Funktion Gewichtsaktualisierungen vornimmt.
Anschließend wird jedes Bild in \texttt{image\_list} aus dem BGR-Farbraum 
in den RGB-Farbraum umgewandelt.

Daraufhin wird eine Datenaugmentationspipeline erstellt und auf das Eingabevideo angewendet. 
Dies umfasst das Zuschneiden des Bildes in der Mitte auf eine Größe von 224x224 
Pixeln, ohne Änderung der Gesamtgröße (1.0 bedeutet keine Größenänderung). 
Schließlich wird das Bild in einen Tensor umgewandelt.
Diese Pipeline wird dann auf die Liste der Bilder angewendet und das Video wird
um eine zusätzliche Dimension erweitert, um eine Batch-Dimension hinzuzufügen.

Die Pixelwerte im Video werden auf den Bereich von -1 bis 1 normalisiert.
Das Video wird auf das Gerät verschoben, auf dem das Modell ausgeführt wird.
Dann wird die Länge des Eingabevideos in \texttt{video\_lgt} in ein Tensor-Objekt
konvertiert und ebenfalls auf das \texttt{output\_device} verschoben. Im Block mit
deaktivierter Gradientenberechnung werden Vorhersagen getroffen und in einem Dictionary
namens \texttt{ret\_dict} gespeichert, das die Demo Applikation dann weiterverarbeiten kann.