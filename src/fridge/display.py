from tkinter import filedialog
import types
import cv2
import glfw
import imgui
import numpy as np

from filedialpy import openFile
from imgui.integrations.glfw import Glfw<PERSON><PERSON><PERSON>
from OpenGL import GL as gl
from time import sleep
from typing import NamedTuple
from ultralytics import YOL<PERSON>

from . import frames
from .session import Session


class Vec2(NamedTuple):
    x: float
    y: float


def _init_window(width, height):
    # Initialize GLFW
    if not glfw.init():
        raise Exception("GLFW initialization failed")

    # ➜ on macOS you must request a 3.3 core profile for ImGui's #version 330 shaders
    glfw.window_hint(glfw.CONTEXT_VERSION_MAJOR, 3)
    glfw.window_hint(glfw.CONTEXT_VERSION_MINOR, 3)
    glfw.window_hint(glfw.OPENGL_PROFILE, glfw.OPENGL_CORE_PROFILE)
    glfw.window_hint(glfw.OPENGL_FORWARD_COMPAT, gl.GL_TRUE)

    # Create window + context
    window = glfw.create_window(width, height, "ImGui + OpenCV", None, None)
    if not window:
        glfw.terminate()
        raise Exception("Failed to create GLFW window")
    glfw.make_context_current(window)

    # ImGui setup
    imgui.create_context()
    impl = GlfwRenderer(window)

    return window, impl


def _draw_bounding_boxes(image_pos: Vec2, image_dims: Vec2, bounding_boxes):
    draw_list = imgui.get_window_draw_list()
    
    for box in bounding_boxes:
        xc, yc, w, h = box  # Normalized coordinates (0-1)

        x = xc - w / 2
        y = yc - h / 2
        
        image_width, image_height = image_dims

        # Convert normalized coordinates to pixel coordinates
        x_min = x * image_width
        y_min = y * image_height
        x_max = (x + w) * image_width
        y_max = (y + h) * image_height
        
        # Adjust to screen-space coordinates based on image position
        top_left = (image_pos[0] + x_min, image_pos[1] + y_min)
        bottom_right = (image_pos[0] + x_max, image_pos[1] + y_max)
        
        # Draw rectangle (red, thickness=2)
        draw_list.add_rect(top_left[0], top_left[1], bottom_right[0], bottom_right[1], 
                          imgui.get_color_u32_rgba(1, 0, 0, 1), thickness=2.0)


class Display:
    def __init__(self, session: Session):
        self.klass_names = session.model.names
        self.session = session
        self.window, self.renderer = _init_window(1400, 920)
        self.is_paused = False
        self.frame_texture = gl.glGenTextures(1)
        self.show_plot = [False] * len(self.klass_names)


    def __del__(self):
        gl.glDeleteTextures([self.frame_texture])
        self.renderer.shutdown()

    
    def _draw_frame(self, frame, render_dims):
        img_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        gl.glBindTexture(gl.GL_TEXTURE_2D, self.frame_texture)
        gl.glTexParameteri(gl.GL_TEXTURE_2D, gl.GL_TEXTURE_MIN_FILTER, gl.GL_LINEAR)
        gl.glTexParameteri(gl.GL_TEXTURE_2D, gl.GL_TEXTURE_MAG_FILTER, gl.GL_LINEAR)
        gl.glTexImage2D(
            gl.GL_TEXTURE_2D, 0, gl.GL_RGB, img_rgb.shape[1], img_rgb.shape[0], 0,
            gl.GL_RGB, gl.GL_UNSIGNED_BYTE, img_rgb
        )
        imgui.image(self.frame_texture, render_dims.x, render_dims.y)


    def update(self, frame: cv2.typing.MatLike | None, bbs: dict[int, np.ndarray]) -> bool:
        """
        Update the display with the given frame and bounding boxes.
        Will run the display loop until the user wishes to see the next frame.
        Returns False if the window is closed and the application should quit.
        """

        step = False
        while True: # do while not paused
            if glfw.window_should_close(self.window):
                return False

            glfw.poll_events()
            self.renderer.process_inputs()

            imgui.new_frame()

            # Create full screen ImGui window
            imgui.begin("Image Window", flags = imgui.WINDOW_NO_TITLE_BAR | imgui.WINDOW_NO_SCROLLBAR)
            window_pos = imgui.get_window_position()
            image_size = imgui.get_window_size()
            image_pos = Vec2(window_pos.x + imgui.get_cursor_pos_x(), window_pos.y + imgui.get_cursor_pos_y())
            if frame is not None:
                self._draw_frame(frame, image_size)
                _draw_bounding_boxes(image_pos, image_size, bbs.values())
            imgui.end()

            imgui.begin("Stats")
            imgui.text(f"Frame #: {self.session.frame_counter}")
            imgui.end()

            imgui.begin("Controls")
            if self.session.frame_generator is None:
                if imgui.button("Open camera"):
                    self.session.set_source(frames.open_camera(index=0), "camera")
                imgui.same_line()
                if imgui.button("Open video"):
                    #file_path = imgui.open_file_dialog("Open video", "data/", "*.mp4")
                    file_path = openFile(title = "Select a video file")
                    if file_path:
                        self.session.set_source(frames.open_video(file_path), file_path)
            else:
                if self.is_paused:
                    if imgui.button("Play"):
                        self.is_paused = False
                    imgui.same_line()
                    if imgui.button("Step"):
                        step = True
                else:
                    if imgui.button("Pause"):
                        self.is_paused = True
                if imgui.button("Close source"):
                    self.session.clear_source()
            imgui.end()

            if len(self.session.action_state.prob_history) > 0:
                imgui.begin("Confidence")
                for i, name in enumerate(self.klass_names):
                    # put text and checkbox in one line.
                    imgui.text(f"{name}: {self.session.action_state.prob_history[-1][i]:.2f}")
                    imgui.same_line()
                    _, self.show_plot[i] = imgui.checkbox(f"Plot {name}", self.show_plot[i])
                    if self.show_plot[i]:
                        imgui.plot_lines("##plot" + name,
                            np.array([x[i] for x in self.session.conf_history], dtype=np.float32),
                            graph_size = (0, 50),
                        )
                imgui.end()

            # Render
            gl.glClear(gl.GL_COLOR_BUFFER_BIT)
            imgui.render()
            self.renderer.render(imgui.get_draw_data())
            glfw.swap_buffers(self.window)

            if step or not self.is_paused:
                break

            sleep(0.01)
        return True
