import cv2

def open_video(file_name: str):
    return _generate_frames(cv2.VideoCapture(file_name))


def open_camera(index = 1, backend = cv2.CAP_ANY, fps=30.0, exposure=-7):
    """
    Record a video from camera 1 with the specified fps and exposure.
    Yields frames from the camera in real-time.
    """

    print("Waiting for open video capture")
    # print capture devices
    cap = cv2.VideoCapture(index, backend)
    if not cap.isOpened():
        print("Error: Could not open camera.")
        return
    print("Successfully opened video capture")

    # cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1920)
    # cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 1080)
    # cap.set(cv2.CAP_PROP_BUFFERSIZE, 3)
    # cap.set(cv2.CAP_PROP_FPS, fps)
    # cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc(*'MJPG'))

    # cap.set(cv2.CAP_PROP_AUTO_EXPOSURE, 0.25)
    # cap.set(cv2.CAP_PROP_EXPOSURE, exposure)
    # cap.set(cv2.CAP_PROP_AUTOFOCUS, 0) # TODO: check if this works

    print("Configured camera settings")

    return _generate_frames(cap)


def list_cameras():
    return dict(enumerate(filter(None, (get_cam_name(i) for i in range(10)))))


def get_cam_name(index):
    cap = cv2.VideoCapture(index, cv2.CAP_DSHOW)
    if not cap.isOpened():
        name = None
        #print(f"Device {index} is not opened")
    else:
        #print(f"Device {index} is opened")
        # ??? there is no way to retrieve a device name
        cap.release()
    return name


def _generate_frames(cap: cv2.VideoCapture):
    while True:
        ret, frame = cap.read()
        if not ret:
            print("Error: Failed to capture image.")
            break
        yield frame
    cap.release()
