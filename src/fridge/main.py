import sys
import argparse


def run_loop(session, interactive):
    if interactive:
        from .display import Display
        print("Starting interactive mode...")
        disp = Display(session)
        for frame in session.frames():
            confs, bbs = session.process(frame)
            if not disp.update(frame, bbs):
                break
    else:
        if not session.has_source():
            print("Error: Non-interactive mode but invalid video source.")
            sys.exit(1)
        for frame in session.frames():
            confs, bbs = session.process(frame)
            print("conf", confs)


def session_from_args(args):
    print("Loading session code...")
    from .session import Session
    print("Creating session...")
    sess = Session()
    print("Created session")
    from . import frames
    if args.camera:
        sess.set_source(frames.open_camera(), "camera")
    elif args.video:
        sess.set_source(frames.open_video(args.video), args.video)
    return sess


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--interactive", action="store_true")
    parser.add_argument("--camera", action="store_true")
    parser.add_argument("--video", type=str)
    args = parser.parse_args()

    if not any([args.interactive, args.camera, args.video]):
        print("Setting interactive mode because no source was given.")
        args.interactive = True

    session = session_from_args(args)

    run_loop(session, args.interactive)


if __name__ == "__main__":
    main()
