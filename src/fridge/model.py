import platform
import cv2
import numpy as np


def is_raspberry_pi() -> bool:
    node = platform.uname().node.lower()
    return "raspberry" in node


class Model:
    """
    Encapsulates model initialization and raw detection logic only.
    """
    def __init__(self, model_path="models/pt/yolo_mix_synth_background.pt", hef_path="models/hailo/yolov11n.hef"):
        self.names = ['bacon', 'carrot', 'cucumber', 'egg', 'eggs', 'ketchup', 'mayonnaise', 'mustard', 'yogurt']
        self.model = None
        self.device = None
        self.hailo_ctx = None
        # Choose backend based on hardware
        if is_raspberry_pi():
            self._init_hailo(hef_path)
        else:
            self._init_pytorch(model_path)

    def _init_pytorch(self, model_path):
        print("PyTorch model loading...")
        # Lazy import heavy libraries only on non-RPi
        import torch
        from ultralytics import YOLO

        self.device = (
            torch.device("cuda") if torch.cuda.is_available()
            else torch.device("mps") if torch.backends.mps.is_available()
            else torch.device("cpu")
        )
        self.model = YOLO(model_path, verbose=False)

    def _init_hailo(self, hef_path):
        print("Hailo model loading...")
        import hailo_platform as hpf # type: ignore
        from ultralytics.utils.ops import non_max_suppression

        hef = hpf.HEF(hef_path)
        vdev = hpf.VDevice()
        cfg = hpf.ConfigureParams.create_from_hef(
            hef, interface=hpf.HailoStreamInterface.PCIe
        )
        ngs = vdev.configure(hef, cfg)
        ng = ngs[0]
        ip = hpf.InputVStreamParams.make_from_network_group(
            ng, quantized=False, format_type=hpf.FormatType.FLOAT32
        )
        op = hpf.OutputVStreamParams.make_from_network_group(
            ng, quantized=False, format_type=hpf.FormatType.FLOAT32
        )
        self.hailo_ctx = (vdev, ng, vdev.allocate_vstreams(ip), vdev.allocate_vstreams(op))
        ng.activate()

    def predict(self, frame, conf_thresh=0.5, iou_thresh=0.45):
        """
        Run detection on a single frame. Returns raw confidence array and bounding boxes dict.
        """
        if self.hailo_ctx:
            return self._predict_hailo(frame, conf_thresh, iou_thresh)
        return self._predict_torch(frame, conf_thresh)

    def _predict_hailo(self, frame, conf_thresh, iou_thresh):
        from ultralytics.utils.ops import non_max_suppression

        vdev, ng, inputs, outputs = self.hailo_ctx
        img = cv2.resize(frame, (640, 640))
        rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        data = (rgb.transpose(2, 0, 1)[None].astype(np.float32) / 255.0).tobytes()

        inputs[0].write(data)
        raw = outputs[0].read()
        arr = np.frombuffer(raw, dtype=np.float32).reshape(1, -1, 7)
        dets = non_max_suppression(arr, conf_thres=conf_thresh, iou_thres=iou_thresh)[0]

        return self._pack_results(frame, dets)

    def _predict_torch(self, frame, conf_thresh):
        # torch and YOLO already imported in init
        results = self.model.predict(
            frame, conf=conf_thresh, stream=True, device=self.device, verbose=False
        )
        boxes = next(results).boxes.cpu()
        return self._pack_results(frame, boxes)

    def _pack_results(self, frame, raw_boxes):
        """
        Convert raw model outputs into (confs, bbs) format.
        """
        h, w = frame.shape[:2]
        names = self.model.names if self.model else []
        confs = np.zeros(len(names), dtype=np.float32)
        bbs = {}

        for box in raw_boxes:
            if hasattr(box, 'cls'):
                cls = int(box.cls)
                conf = float(box.conf)
                xywhn = box.xywhn[0].numpy()
            else:
                *xyxy, conf, cls = box.tolist()
                cls = int(cls)
                x1, y1, x2, y2 = xyxy
                xywhn = np.array([((x1+x2)/2)/w, ((y1+y2)/2)/h, (x2-x1)/w, (y2-y1)/h], dtype=np.float32)

            confs[cls] = conf
            bbs[cls] = xywhn

        return confs, bbs
