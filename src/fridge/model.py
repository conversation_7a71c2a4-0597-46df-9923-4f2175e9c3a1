import platform
import cv2
import numpy as np


def dbg(x):
    print(x)
    return x

def is_raspberry_pi() -> bool:
    return dbg(platform.system()) == "Linux" and dbg(platform.machine()) == "aarch64"


class Model:
    """
    Encapsulates model initialization and raw detection logic only.
    """
    def __init__(self, model_path="models/pt/yolo_mix_synth_background.pt", hef_path="models/hailo/yolov11n.hef"):
        self.names = ['bacon', 'carrot', 'cucumber', 'egg', 'eggs', 'ketchup', 'mayonnaise', 'mustard', 'yogurt']
        self.model = None
        self.device = None
        self.is_hailo = False
        # Choose backend based on hardware
        if is_raspberry_pi():
            self._init_hailo(hef_path)
            self.is_hailo = True
        else:
            self._init_pytorch(model_path)

    def _init_pytorch(self, model_path):
        print("PyTorch model loading...")
        # Lazy import heavy libraries only on non-RPi
        import torch
        from ultralytics import YOLO

        self.device = (
            torch.device("cuda") if torch.cuda.is_available()
            else torch.device("mps") if torch.backends.mps.is_available()
            else torch.device("cpu")
        )
        self.model = YOLO(model_path, verbose=False)

    def _init_hailo(self, hef_path):
        print("Hailo model loading...")
        import hailo_platform as hpf # type: ignore

        # Load HEF file
        self.hef = hpf.HEF(hef_path)

        # Get input/output stream info
        self.input_vstream_info = self.hef.get_input_vstream_infos()[0]
        self.output_vstream_infos = self.hef.get_output_vstream_infos()

        # Create VDevice and configure network
        self.vdevice = hpf.VDevice()
        configure_params = hpf.ConfigureParams.create_from_hef(
            self.hef, interface=hpf.HailoStreamInterface.PCIe
        )
        self.network_group = self.vdevice.configure(self.hef, configure_params)[0]
        self.network_group_params = self.network_group.create_params()

        # Create VStream parameters
        self.input_vstreams_params = hpf.InputVStreamParams.make_from_network_group(
            self.network_group, quantized=False, format_type=hpf.FormatType.FLOAT32
        )
        self.output_vstreams_params = hpf.OutputVStreamParams.make_from_network_group(
            self.network_group, quantized=False, format_type=hpf.FormatType.FLOAT32
        )

        print(f"Input shape: {self.input_vstream_info.shape}")
        print(f"Output shapes: {[info.shape for info in self.output_vstream_infos]}")

    def predict(self, frame, conf_thresh=0.5, iou_thresh=0.45):
        """
        Run detection on a single frame. Returns raw confidence array and bounding boxes dict.
        """
        if self.is_hailo:
            return self._predict_hailo(frame, conf_thresh, iou_thresh)
        return self._predict_torch(frame, conf_thresh)

    def _predict_hailo(self, frame, conf_thresh, iou_thresh):
        from ultralytics.utils.ops import non_max_suppression
        import hailo_platform as hpf # type: ignore

        # Preprocess image to match model input requirements
        input_shape = self.input_vstream_info.shape
        img = cv2.resize(frame, (input_shape[1], input_shape[0]))  # width, height
        rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

        # Normalize and prepare input data
        input_data = rgb.astype(np.float32) / 255.0
        input_data = np.expand_dims(input_data, axis=0)  # Add batch dimension

        # Prepare input dictionary
        input_dict = {self.input_vstream_info.name: input_data}

        # Run inference using high-level API
        with self.network_group.activate(self.network_group_params):
            with hpf.InferVStreams(self.network_group, self.input_vstreams_params, self.output_vstreams_params) as infer_pipeline:
                results = infer_pipeline.infer(input_dict)

                # Get the main output - check if it's already post-processed
                output_key = list(results.keys())[0]
                output_data = results[output_key]

                print(f"Output key: {output_key}")
                print(f"Output data type: {type(output_data)}")

                # Check if this is already post-processed (contains 'nms' or 'postprocess' in key)
                is_postprocessed = 'nms' in output_key.lower() or 'postprocess' in output_key.lower()

                if is_postprocessed:
                    print("Detected post-processed output, skipping NMS")
                    # Output is already post-processed, convert to expected format
                    if isinstance(output_data, list):
                        # Usually a list of detections
                        if len(output_data) > 0 and isinstance(output_data[0], (list, tuple, np.ndarray)):
                            # Convert list of detections to numpy array
                            dets = np.array(output_data[0]) if len(output_data) > 0 else np.array([])
                        else:
                            dets = np.array(output_data)
                    else:
                        dets = output_data

                    # Ensure dets is 2D array [num_detections, detection_data]
                    if len(dets.shape) == 1 and len(dets) > 0:
                        # Single detection, reshape
                        dets = dets.reshape(1, -1)
                    elif len(dets.shape) == 0:
                        # No detections
                        dets = np.array([]).reshape(0, 6)  # Empty array with correct shape

                    print(f"Post-processed detections shape: {dets.shape}")
                    return self._pack_results(frame, dets)

                else:
                    print("Raw output detected, applying NMS")
                    # Convert to numpy array if it's a list
                    if isinstance(output_data, list):
                        output_data = np.array(output_data)

                    # Ensure we have a numpy array
                    if not isinstance(output_data, np.ndarray):
                        output_data = np.array(output_data)

                    # Reshape output for NMS processing
                    # YOLO output is typically [batch, detections, 5+classes] where 5 = x,y,w,h,conf
                    if len(output_data.shape) == 3:
                        arr = output_data
                    elif len(output_data.shape) == 2:
                        # Add batch dimension if missing
                        arr = np.expand_dims(output_data, axis=0)
                    else:
                        # Try to reshape to expected format
                        total_elements = output_data.size
                        elements_per_detection = 5 + len(self.names)  # x,y,w,h,conf + classes
                        num_detections = total_elements // elements_per_detection
                        arr = output_data.reshape(1, num_detections, elements_per_detection)

                    # Apply NMS
                    dets = non_max_suppression(arr, conf_thres=conf_thresh, iou_thres=iou_thresh)[0]
                    return self._pack_results(frame, dets)

    def _predict_torch(self, frame, conf_thresh):
        # torch and YOLO already imported in init
        results = self.model.predict(
            frame, conf=conf_thresh, stream=True, device=self.device, verbose=False
        )
        boxes = next(results).boxes.cpu()
        return self._pack_results(frame, boxes)

    def _pack_results(self, frame, raw_boxes):
        """
        Convert raw model outputs into (confs, bbs) format.
        """
        h, w = frame.shape[:2]
        names = self.model.names if self.model else self.names
        confs = np.zeros(len(names), dtype=np.float32)
        bbs = {}

        for box in raw_boxes:
            if hasattr(box, 'cls'):
                cls = int(box.cls)
                conf = float(box.conf)
                xywhn = box.xywhn[0].numpy()
            else:
                *xyxy, conf, cls = box.tolist()
                cls = int(cls)
                x1, y1, x2, y2 = xyxy
                xywhn = np.array([((x1+x2)/2)/w, ((y1+y2)/2)/h, (x2-x1)/w, (y2-y1)/h], dtype=np.float32)

            confs[cls] = conf
            bbs[cls] = xywhn

        return confs, bbs
