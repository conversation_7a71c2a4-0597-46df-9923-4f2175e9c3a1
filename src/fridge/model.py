import platform
import cv2
import numpy as np


def dbg(x):
    print(x)
    return x

def is_raspberry_pi() -> bool:
    return dbg(platform.system()) == "Linux" and dbg(platform.machine()) == "aarch64"


class Model:
    """
    Encapsulates model initialization and raw detection logic only.
    """
    def __init__(self, model_path="models/pt/yolo_mix_synth_background.pt", hef_path="models/hailo/yolov11n.hef"):
        self.names = ['bacon', 'carrot', 'cucumber', 'egg', 'eggs', 'ketchup', 'mayonnaise', 'mustard', 'yogurt']
        self.model = None
        self.device = None
        self.is_hailo = False
        # Choose backend based on hardware
        if is_raspberry_pi():
            self._init_hailo(hef_path)
            self.is_hailo = True
        else:
            self._init_pytorch(model_path)

    def _init_pytorch(self, model_path):
        print("PyTorch model loading...")
        # Lazy import heavy libraries only on non-RPi
        import torch
        from ultralytics import YOLO

        self.device = (
            torch.device("cuda") if torch.cuda.is_available()
            else torch.device("mps") if torch.backends.mps.is_available()
            else torch.device("cpu")
        )
        self.model = YOLO(model_path, verbose=False)

    def _init_hailo(self, hef_path):
        print("Hailo model loading...")
        import hailo_platform as hpf # type: ignore

        # Load HEF file
        self.hef = hpf.HEF(hef_path)

        # Get input/output stream info
        self.input_vstream_info = self.hef.get_input_vstream_infos()[0]
        self.output_vstream_infos = self.hef.get_output_vstream_infos()

        # Create VDevice and configure network
        self.vdevice = hpf.VDevice()
        configure_params = hpf.ConfigureParams.create_from_hef(
            self.hef, interface=hpf.HailoStreamInterface.PCIe
        )
        self.network_group = self.vdevice.configure(self.hef, configure_params)[0]
        self.network_group_params = self.network_group.create_params()

        # Create VStream parameters
        self.input_vstreams_params = hpf.InputVStreamParams.make_from_network_group(
            self.network_group, quantized=False, format_type=hpf.FormatType.FLOAT32
        )
        self.output_vstreams_params = hpf.OutputVStreamParams.make_from_network_group(
            self.network_group, quantized=False, format_type=hpf.FormatType.FLOAT32
        )

        print(f"Input shape: {self.input_vstream_info.shape}")
        print(f"Output shapes: {[info.shape for info in self.output_vstream_infos]}")

    def predict(self, frame, conf_thresh=0.5, iou_thresh=0.45):
        """
        Run detection on a single frame. Returns raw confidence array and bounding boxes dict.
        """
        if self.is_hailo:
            return self._predict_hailo(frame, conf_thresh, iou_thresh)
        return self._predict_torch(frame, conf_thresh)

    def _predict_hailo(self, frame, conf_thresh, iou_thresh):
        from ultralytics.utils.ops import non_max_suppression
        import hailo_platform as hpf # type: ignore

        # Preprocess image to match model input requirements
        input_shape = self.input_vstream_info.shape
        img = cv2.resize(frame, (input_shape[1], input_shape[0]))  # width, height
        rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

        # Normalize and prepare input data
        input_data = rgb.astype(np.float32) / 255.0
        input_data = np.expand_dims(input_data, axis=0)  # Add batch dimension

        # Prepare input dictionary
        input_dict = {self.input_vstream_info.name: input_data}

        # Run inference using high-level API
        with self.network_group.activate(self.network_group_params):
            with hpf.InferVStreams(self.network_group, self.input_vstreams_params, self.output_vstreams_params) as infer_pipeline:
                results = infer_pipeline.infer(input_dict)

                # Debug: Print results structure
                print(f"Results keys: {list(results.keys())}")
                print(f"Results types: {[type(v) for v in results.values()]}")
                if results:
                    first_result = list(results.values())[0]
                    print(f"First result type: {type(first_result)}")
                    if hasattr(first_result, 'shape'):
                        print(f"First result shape: {first_result.shape}")
                    elif isinstance(first_result, (list, tuple)):
                        print(f"First result length: {len(first_result)}")

                # Get the main output (usually the first or last output)
                # For YOLO models, we typically want the detection output
                output_data = None
                for output_info in self.output_vstream_infos:
                    if output_info.name in results:
                        output_data = results[output_info.name]
                        break

                if output_data is None:
                    # Fallback to first available output
                    output_data = list(results.values())[0]

                # Convert to numpy array if it's a list
                if isinstance(output_data, list):
                    output_data = np.array(output_data)

                # Ensure we have a numpy array
                if not isinstance(output_data, np.ndarray):
                    output_data = np.array(output_data)

                # Reshape output for NMS processing
                # YOLO output is typically [batch, detections, 5+classes] where 5 = x,y,w,h,conf
                if len(output_data.shape) == 3:
                    arr = output_data
                elif len(output_data.shape) == 2:
                    # Add batch dimension if missing
                    arr = np.expand_dims(output_data, axis=0)
                else:
                    # Try to reshape to expected format
                    # Assume last dimension contains the detection data
                    total_elements = output_data.size
                    # Common YOLO output format: [batch, num_detections, 5+num_classes]
                    # For 9 classes: 5 + 9 = 14 elements per detection
                    elements_per_detection = 5 + len(self.names)  # x,y,w,h,conf + classes
                    num_detections = total_elements // elements_per_detection
                    arr = output_data.reshape(1, num_detections, elements_per_detection)

                # Apply NMS
                dets = non_max_suppression(arr, conf_thres=conf_thresh, iou_thres=iou_thresh)[0]

                return self._pack_results(frame, dets)

    def _predict_torch(self, frame, conf_thresh):
        # torch and YOLO already imported in init
        results = self.model.predict(
            frame, conf=conf_thresh, stream=True, device=self.device, verbose=False
        )
        boxes = next(results).boxes.cpu()
        return self._pack_results(frame, boxes)

    def _pack_results(self, frame, raw_boxes):
        """
        Convert raw model outputs into (confs, bbs) format.
        """
        h, w = frame.shape[:2]
        names = self.model.names if self.model else self.names
        confs = np.zeros(len(names), dtype=np.float32)
        bbs = {}

        for box in raw_boxes:
            if hasattr(box, 'cls'):
                cls = int(box.cls)
                conf = float(box.conf)
                xywhn = box.xywhn[0].numpy()
            else:
                *xyxy, conf, cls = box.tolist()
                cls = int(cls)
                x1, y1, x2, y2 = xyxy
                xywhn = np.array([((x1+x2)/2)/w, ((y1+y2)/2)/h, (x2-x1)/w, (y2-y1)/h], dtype=np.float32)

            confs[cls] = conf
            bbs[cls] = xywhn

        return confs, bbs
